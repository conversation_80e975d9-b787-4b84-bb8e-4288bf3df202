# file_parser.py

import os
import threading
import re
from typing import Dict, Callable, Any, Optional, List, Tuple
from lxml import etree
# functools.lru_cache đã được loại bỏ

import logging


from constants import (MAX_ENTITY_RESOLUTION_DEPTH, SKIP_IMAGE_ENTITY_EXTS,
                       SUPPORTED_ENCODINGS, FILE_EXTENSIONS)

from sqlite_service import ProjectDataService

logger = logging.getLogger(__name__)

# ================================
# Constants & Regex
# ================================
RE_DOCTYPE = re.compile(r'(<!DOCTYPE\s+[^\[]+\[(.*?)\]>)', re.DOTALL)
RE_ENTITY_REF = re.compile(r'&([\w\.\-%]+);')
XML_BUILTIN_ENTITIES = {"lt": "<", "gt": ">", "amp": "&", "quot": '"', "apos": "'"}

# ================================
# File Read Helper
# ================================
# Đã loại bỏ @lru_cache để tránh các vấn đề về an toàn luồng (thread-safety)
def _safe_read_file(path: str) -> str:
    path = os.path.normpath(path)
    for enc in SUPPORTED_ENCODINGS:
        try:
            with open(path, 'r', encoding=enc) as f:
                content = f.read()
                return content[1:] if content.startswith('\ufeff') else content
        except (UnicodeDecodeError, IOError): continue
    raise IOError(f"Không thể đọc file '{os.path.basename(path)}'")

class AnalysisService:
    """
    Lớp chịu trách nhiệm cho toàn bộ logic nghiệp vụ liên quan đến việc
    phân tích file. Nó chạy trong một luồng riêng và giao tiếp với
    Controller thông qua các callback để cập nhật UI.
    """
    def __init__(self,
                 on_progress_update: Callable[[str, int], None],
                 on_single_file_complete: Callable[[str, Dict], None],
                 on_batch_analysis_finished: Callable[[str], None],
                 on_file_parsed: Callable[[str, Dict], None],
                 is_running_checker: Callable[[], bool]):
        self.on_progress_update = on_progress_update
        self.on_single_file_complete = on_single_file_complete
        self.on_batch_analysis_finished = on_batch_analysis_finished
        self.on_file_parsed = on_file_parsed
        self.is_running = is_running_checker
        self.analysis_lock = threading.Lock()

    def _analyze_and_cache_single_file(self, file_path: str, sqlite_service: ProjectDataService) -> tuple[bool, dict]:
        """
        Hàm lõi để phân tích một file, cập nhật cache và trả về kết quả.
        """
        try:
            info = FileParser.extract_entity_info(file_path)
            if sqlite_service:
                sqlite_service.update_cache_from_parser(file_path, info)
            
            self.on_file_parsed(file_path, info)

            if not info.get("analysis_failed"):
                logger.info(f"Phân tích thành công file: {file_path}")
                return True, info
            else:
                logger.warning(f"Phân tích file {file_path} không thành công, đã ghi nhận lỗi vào cache.")
                return False, info

        except Exception as e:
            logger.error(f"Lỗi nghiêm trọng khi phân tích file: {file_path}", exc_info=True)
            error_info = {
                "analysis_failed": True,
                "dir_info": {"error": f"Lỗi nghiêm trọng: {e}"},
                "original_content": ""
            }
            if sqlite_service:
                sqlite_service.update_cache_from_parser(file_path, error_info)
            
            self.on_file_parsed(file_path, error_info)
            return False, error_info

    def run_batch_analysis(self, files_to_process: List[str], sqlite_service: ProjectDataService, task_name_for_ui: str):
        """
        Hàm thống nhất để chạy các tác vụ phân tích hàng loạt trên một luồng riêng.
        """
        def task():
            if not self.analysis_lock.acquire(blocking=False):
                self.on_progress_update("Một tiến trình phân tích khác đang chạy.", 4000)
                return

            try:
                total = len(files_to_process)
                self.on_progress_update(f"Bắt đầu {task_name_for_ui} {total} files...", 5000)

                successful_parses = 0
                failed_parses = 0

                for i, file_path in enumerate(files_to_process):
                    if not self.is_running():
                        self.on_progress_update(f"Đã dừng {task_name_for_ui}.", 4000)
                        break
                    
                    status_msg = f"Đang {task_name_for_ui} file {i+1}/{total}: {os.path.basename(file_path)}"
                    self.on_progress_update(status_msg, 1000)

                    success, _ = self._analyze_and_cache_single_file(file_path, sqlite_service)
                    if success:
                        successful_parses += 1
                    else:
                        failed_parses += 1
                
                if self.is_running():
                    final_status = f"Hoàn tất {task_name_for_ui}. Thành công: {successful_parses}, Lỗi: {failed_parses}, Tổng: {total}."
                    self.on_batch_analysis_finished(final_status)

            finally:
                self.analysis_lock.release()
        
        threading.Thread(target=task, daemon=True).start()


    def run_single_file_analysis(self, file_path: str, sqlite_service: ProjectDataService):
        """
        Chạy phân tích cho một file duy nhất trên một luồng riêng và gọi callback khi hoàn tất.
        """
        def task():
            if not self.analysis_lock.acquire(blocking=False):
                self.on_progress_update("Một tiến trình phân tích khác đang chạy. Vui lòng thử lại sau.", 4000)
                return

            try:
                self.on_progress_update(f"Đang phân tích file: {os.path.basename(file_path)}...", 10000)
                _, info = self._analyze_and_cache_single_file(file_path, sqlite_service)
                self.on_single_file_complete(file_path, info)
            finally:
                self.analysis_lock.release()
        
        threading.Thread(target=task, daemon=True).start()

# ================================
# Entity Resolution Logic
# ================================
class EntityResolver:
    """
    Lớp chuyên trách việc phân tích DTD, tìm, đọc và phân giải các entity,
    bao gồm cả việc xử lý logic của các khối điều kiện.
    """
    RE_PARAM_ENTITY_SYSTEM = re.compile(r'<!ENTITY\s+%\s*([\w\.\-]+)\s+SYSTEM\s+"([^"]+)"')
    RE_GENERAL_ENTITY_SYSTEM = re.compile(r'<!ENTITY\s+([\w\.\-]+)\s+SYSTEM\s+"([^"]+)"')
    RE_ENTITY_DEF = re.compile(r'<!ENTITY\s+([\w\.\-]+)\s+"([^"]*)">')
    RE_INTERNAL_ENTITY_FULL = re.compile(r'<!ENTITY\s+(%?)\s*([\w\.\-]+)\s+"[^"]*">')
    RE_CONDITIONAL_BLOCK = re.compile(r'<!\[\s*(%[\w\.\-]+;|\w+)\s*\[(.*?)\]\]>', re.DOTALL)

    def __init__(self, base_dir: str, dtd_content: str, initial_source_path: str):
        self.base_dir = base_dir
        self.dtd_content = dtd_content
        self.initial_source_path = initial_source_path
        self.entity_map: Dict[str, str] = dict(XML_BUILTIN_ENTITIES)
        self.entity_source_map: Dict[str, str] = {}
        self.entity_structures: List[Dict[str, Any]] = []
        self.param_entity_definitions: Dict[str, str] = {}

    def resolve_and_extract(self):
        """
        Thực hiện toàn bộ quá trình: phân giải giá trị và trích xuất cấu trúc.
        """
        self.param_entity_definitions = self._build_param_entity_map()
        expanded_dtd = self._expand_dtd(self.param_entity_definitions)
        self._build_general_entity_map(expanded_dtd)
        self._resolve_nested_entities()
        self._extract_declarations_recursively(self.dtd_content, self.initial_source_path, self.base_dir, 0, set())
        return self.entity_map, self.entity_source_map, self.entity_structures

    def _build_param_entity_map(self) -> Dict[str, str]:
        param_defs, processed_urls = {}, set()
        queue = [(self.base_dir, self.dtd_content)]
        while queue:
            current_base, dtd_chunk = queue.pop(0)
            for m in self.RE_PARAM_ENTITY_SYSTEM.finditer(dtd_chunk):
                name, rel_path = m.groups()
                if rel_path.lower().endswith(SKIP_IMAGE_ENTITY_EXTS): continue
                include_path = os.path.normpath(os.path.join(current_base, rel_path))
                if include_path in processed_urls: continue
                processed_urls.add(include_path)
                if os.path.isfile(include_path):
                    try:
                        file_content = _safe_read_file(include_path).strip()
                        param_defs[name], self.entity_source_map[name] = file_content, include_path
                        queue.append((os.path.dirname(include_path), file_content))
                    except Exception as e: logger.warning(f"Lỗi đọc parameter entity file {include_path}: {e}")
                else: logger.warning(f"Không tìm thấy parameter entity file: {include_path}")
        return param_defs

    def _expand_dtd(self, param_entity_definitions: Dict[str, str]) -> str:
        temp_expanded_dtd = re.sub(self.RE_CONDITIONAL_BLOCK, '', self.dtd_content)
        for _ in range(MAX_ENTITY_RESOLUTION_DEPTH):
            did_expand = False
            def repl(m):
                nonlocal did_expand
                name = m.group(1)
                if name in param_entity_definitions: did_expand = True; return param_entity_definitions[name]
                return m.group(0)
            new_dtd = re.sub(r'%([\w\.\-]+);', repl, temp_expanded_dtd)
            if not did_expand: break
            temp_expanded_dtd = new_dtd
        return temp_expanded_dtd

    def _build_general_entity_map(self, expanded_dtd: str):
        for m in self.RE_ENTITY_DEF.finditer(expanded_dtd):
            name, val = m.groups()
            if val.strip().lower().endswith(SKIP_IMAGE_ENTITY_EXTS): continue
            self.entity_map[name] = val
            self.entity_source_map.setdefault(name, self.base_dir)
        for m in self.RE_GENERAL_ENTITY_SYSTEM.finditer(expanded_dtd):
            name, rel_path = m.groups()
            if rel_path.lower().endswith(SKIP_IMAGE_ENTITY_EXTS): continue
            include_path = os.path.normpath(os.path.join(self.base_dir, rel_path))
            if os.path.isfile(include_path):
                try: self.entity_map[name], self.entity_source_map[name] = _safe_read_file(include_path), include_path
                except Exception as e: logger.warning(f"Lỗi đọc general entity file {include_path}: {e}")
            else: logger.warning(f"Không tìm thấy general entity file: {include_path}")

    def _resolve_nested_entities(self):
        for _ in range(MAX_ENTITY_RESOLUTION_DEPTH):
            changed = False
            for k, v in self.entity_map.items():
                if not isinstance(v, str) or '&' not in v: continue
                new_v = RE_ENTITY_REF.sub(lambda m: self.entity_map.get(m.group(1), m.group(0)), v)
                if new_v != v: self.entity_map[k], changed = new_v, True
            if not changed: break
            
    def _extract_declarations_recursively(self, dtd_content: str, source_path: str, base_dir: str, depth: int, processed_files: set, condition_context: dict = None):
        if depth > MAX_ENTITY_RESOLUTION_DEPTH or source_path in processed_files: return
        processed_files.add(source_path)
        condition_context = condition_context or {'flag': None, 'state': None}
        remaining_content = dtd_content
        for m in self.RE_CONDITIONAL_BLOCK.finditer(dtd_content):
            block_content, flag_raw, content_inside = m.group(0), m.group(1), m.group(2)
            flag = flag_raw.strip(' %;')
            resolved_state = self.param_entity_definitions.get(flag, flag).upper()
            state = 'INCLUDE' if 'INCLUDE' in resolved_state else 'IGNORE'
            self._extract_declarations_recursively(content_inside, source_path, base_dir, depth, processed_files, {'flag': flag, 'state': state})
            remaining_content = remaining_content.replace(block_content, '')
        self._extract_entities_from_chunk(remaining_content, source_path, base_dir, depth, processed_files, condition_context)

    def _extract_entities_from_chunk(self, dtd_chunk: str, source_path: str, base_dir: str, depth: int, processed_files: set, condition_context: dict):
        default_keys = {'parent_entity': None, 'condition_flag': condition_context.get('flag'), 'condition_state': condition_context.get('state')}
        for m in self.RE_PARAM_ENTITY_SYSTEM.finditer(dtd_chunk):
            name, rel_path = m.groups()
            if rel_path.lower().endswith(SKIP_IMAGE_ENTITY_EXTS): continue
            self.entity_structures.append({'entity_name': name, 'entity_type': 'external_parameter', 'is_parameter': True, 'is_external': True, 'source_file': source_path, 'depth': depth, **default_keys})
            include_path = os.path.normpath(os.path.join(base_dir, rel_path))
            if os.path.isfile(include_path):
                try: self._extract_declarations_recursively(_safe_read_file(include_path), include_path, os.path.dirname(include_path), depth + 1, processed_files, condition_context)
                except Exception as e: logger.warning(f"Không thể đọc/phân tích file entity ngoài {include_path}: {e}")
        for m in self.RE_GENERAL_ENTITY_SYSTEM.finditer(dtd_chunk):
            name, rel_path = m.groups()
            if rel_path.lower().endswith(SKIP_IMAGE_ENTITY_EXTS): continue
            self.entity_structures.append({'entity_name': name, 'entity_type': 'external_general', 'is_parameter': False, 'is_external': True, 'source_file': source_path, 'depth': depth, **default_keys})
        for m in self.RE_INTERNAL_ENTITY_FULL.finditer(dtd_chunk):
            is_param_char, name = m.groups()
            is_param = bool(is_param_char.strip())
            self.entity_structures.append({'entity_name': name, 'entity_type': 'parameter' if is_param else 'general', 'is_parameter': is_param, 'is_external': False, 'source_file': source_path, 'depth': depth, **default_keys})

# ================================
# Core Parser
# ================================
class _Parser:
    def __init__(self, file_path: str):
        self.file_path = os.path.normpath(file_path)
        self.raw_content = ""
        self.root: Optional[etree._Element] = None
        self.ns_map = {}
        self.entity_map: Dict[str, str] = {}
        self.entity_source_map: Dict[str, str] = {}
        self.entity_structures: List[Dict[str, Any]] = [] 
        self.final_doctype_full: Optional[str] = None
        self.has_encrypted_content: bool = False

    def parse(self) -> Dict[str, Any]:
        if not self._read_content():
            return self._return_failure(f"Không thể đọc file: {os.path.basename(self.file_path)}", "")
        self._process_doctype_and_entities()
        if not self._parse_xml_tree():
            return self._return_failure("Lỗi cú pháp XML, không thể phân tích.", self.raw_content)
        return self._extract_data()

    def _read_content(self) -> bool:
        try:
            self.raw_content = _safe_read_file(self.file_path)
            temp_content = self.raw_content
            for pattern in [re.compile(r'<Encrypted>.*?</Encrypted>', re.DOTALL | re.IGNORECASE), re.compile(r'<!\[CDATA\[<Encrypted>.*?</Encrypted>\]\]>', re.DOTALL | re.IGNORECASE), re.compile(r'FastBusiness\.Encryption\.Begin.*?FastBusiness\.Encryption\.End', re.DOTALL | re.IGNORECASE)]:
                if pattern.search(temp_content):
                    self.has_encrypted_content = True
                    temp_content = pattern.sub('<!--Encrypted-->', temp_content)
            self.raw_content = temp_content
            return True
        except IOError as e:
            logger.error(f"Lỗi đọc file {self.file_path}: {e}")
            return False

    def _process_doctype_and_entities(self):
        doctype_match = RE_DOCTYPE.search(self.raw_content)
        if doctype_match:
            self.final_doctype_full = doctype_match.group(1)
            doctype_content = doctype_match.group(2)
            base_dir = os.path.dirname(self.file_path)
            resolver = EntityResolver(base_dir, doctype_content, self.file_path)
            self.entity_map, self.entity_source_map, self.entity_structures = resolver.resolve_and_extract()
        else:
            self.final_doctype_full, self.entity_map, self.entity_source_map, self.entity_structures = None, dict(XML_BUILTIN_ENTITIES), {}, []

    def _parse_xml_tree(self) -> bool:
        if self.has_encrypted_content: return self._parse_with_fallback()
        try:
            clean_xml = re.sub(r'^\s*<\?xml[^>]*\?>', '', self.raw_content, count=1).strip()
            if not clean_xml: return False
            parser = etree.XMLParser(resolve_entities=True, recover=True, no_network=True, remove_comments=True, load_dtd=True)
            entity_replacement_map = self.entity_map.copy()
            for key in XML_BUILTIN_ENTITIES:
                if key in entity_replacement_map: del entity_replacement_map[key]
            resolved_xml_for_parsing = clean_xml
            for entity_name, entity_value in entity_replacement_map.items():
                resolved_xml_for_parsing = resolved_xml_for_parsing.replace(f'&{entity_name};', entity_value)
            self.root = etree.fromstring(resolved_xml_for_parsing.encode('utf-8'), parser)
            if self.root is None: return self._parse_with_fallback()
            self.ns_map = {k if k is not None else 'df': v for k, v in self.root.nsmap.items()}
            if None in self.root.nsmap: self.ns_map['df'] = self.root.nsmap[None]
            return True
        except (etree.XMLSyntaxError, ValueError) as ex:
            logger.warning(f"Lỗi cú pháp XML trong '{self.file_path}', thử lại không DTD: {ex}")
            return self._parse_with_fallback()

    def _parse_with_fallback(self) -> bool:
        try:
            content_no_doctype = RE_DOCTYPE.sub('', self.raw_content)
            clean_xml = re.sub(r'^\s*<\?xml[^>]*\?>', '', content_no_doctype, count=1).strip()
            if not clean_xml: return False
            parser = etree.XMLParser(resolve_entities=False, recover=True, no_network=True, remove_comments=True)
            self.root = etree.fromstring(clean_xml.encode('utf-8'), parser)
            if self.root is None: return False
            self.ns_map = {k if k is not None else 'df': v for k, v in self.root.nsmap.items()}
            if None in self.root.nsmap: self.ns_map['df'] = self.root.nsmap[None]
            return True
        except Exception as fallback_ex:
            logger.error(f"Phân tích lại file '{self.file_path}' cũng thất bại: {fallback_ex}")
            return False

    def _extract_data(self) -> Dict[str, Any]:
        if self.root is None: return self._return_failure("Cây XML không hợp lệ.", self.raw_content)
        all_categories = self._extract_all_categories(self.root)
        if self.ns_map and 'df' in self.ns_map: top_level_fields_node = self.root.find('df:fields', self.ns_map)
        else: top_level_fields_node = self.root.find('fields')
        return {"dir_info": self._extract_dir_info(), "fields": self._extract_fields(top_level_fields_node, all_categories), "forms": self._extract_forms(all_categories), "categories": all_categories, "final_doctype": self.final_doctype_full, "full_content": etree.tostring(self.root, encoding='unicode', pretty_print=True), "original_content": self.raw_content, "entity_map": self.entity_map, "entities": self.entity_structures, "entity_references": sorted(list(set(RE_ENTITY_REF.findall(self.raw_content)))), "analysis_failed": False}
        
    def _resolve_string_with_source(self, text: Optional[str]) -> dict:
        if text is None: return {'value': '', 'sources': []}
        sources = []
        def find_sources(current_text):
            matches = list(RE_ENTITY_REF.finditer(current_text))
            if not matches: return current_text
            resolved_parts, last_end = [], 0
            for m in matches:
                resolved_parts.append(current_text[last_end:m.start()])
                name = m.group(1)
                if name in self.entity_map:
                    source_path = self.entity_source_map.get(name)
                    sources.append({'name': name, 'value': self.entity_map[name], 'file': os.path.basename(source_path) if source_path else 'Internal DOCTYPE'})
                    resolved_parts.append(find_sources(self.entity_map[name]))
                else: resolved_parts.append(m.group(0))
                last_end = m.end()
            resolved_parts.append(current_text[last_end:])
            return "".join(resolved_parts)
        return {'value': find_sources(text), 'sources': sources}

    def _extract_dir_info(self) -> Dict[str, Any]:
        if self.root is None: return {}
        dir_info: Dict[str, Any] = {}
        try:
            dir_info['tag_name'] = etree.QName(self.root.tag).localname
            dir_info.update(self.root.attrib)
            for child_name, attr_v, attr_e in [('title', 'title_v', 'title_e'), ('subTitle', 'subTitle_v', 'subTitle_e'), ('header', 'header_v', 'header_e')]:
                node = self.root.find(f'df:{child_name}', self.ns_map) if self.ns_map and 'df' in self.ns_map else self.root.find(child_name)
                if node is not None: dir_info[attr_v], dir_info[attr_e] = node.get('v', ''), node.get('e', '')
        except Exception as e: logger.warning(f"Lỗi trong _extract_dir_info: {e}", exc_info=True)
        return dir_info

    def _extract_all_categories(self, root_node: etree._Element) -> List[dict]:
        if root_node is None: return []
        categories, ns = [], self.ns_map if self.ns_map and 'df' in self.ns_map else None
        category_xpaths = ['.//df:views/df:view/df:categories/df:category', './/df:categories/df:category', './/df:category'] if ns else ['.//views/view/categories/category', './/categories/category', './/category']
        header_xpath = 'df:header' if ns else 'header'
        for category_xpath in category_xpaths:
            found_categories = root_node.xpath(category_xpath, namespaces=ns)
            if found_categories:
                for cat_node in found_categories:
                    header_node = cat_node.find(header_xpath, namespaces=ns)
                    categories.append({'index': cat_node.get('index', ''), 'header_v': header_node.get('v', '') if header_node is not None else '', 'header_e': header_node.get('e', '') if header_node is not None else ''})
                break
        if not categories and hasattr(self, 'raw_content'): categories = self._extract_categories_from_raw_content()
        unique_categories, seen_indices = [], set()
        for cat in categories:
            index = cat.get('index')
            if index and index not in seen_indices: unique_categories.append(cat); seen_indices.add(index)
        return unique_categories

    def _extract_categories_from_raw_content(self) -> List[dict]:
        categories = []
        for pattern in [re.compile(r'<category\s+([^>]*?)>\s*<header\s+v="([^"]*?)"\s+e="([^"]*?)"\s*/>\s*</category>', re.DOTALL | re.IGNORECASE), re.compile(r'<category\s+([^>]*?)>\s*<header\s+v="([^"]*?)"\s+e="([^"]*?)"\s*></header>\s*</category>', re.DOTALL | re.IGNORECASE), re.compile(r'<category\s+([^>]*?)>\s*<header\s+v="([^"]*?)"\s*/>\s*</category>', re.DOTALL | re.IGNORECASE)]:
            for match in pattern.finditer(self.raw_content):
                groups, attributes_str = match.groups(), match.group(1)
                header_v, header_e = (groups[1] if len(groups) > 1 else ''), (groups[2] if len(groups) > 2 else '')
                index = (re.search(r'index\s*=\s*["\']([^"\']*)["\']', attributes_str).group(1)) if re.search(r'index\s*=\s*["\']([^"\']*)["\']', attributes_str) else ''
                if index and index.strip() and index != '-1': categories.append({'index': index, 'header_v': header_v, 'header_e': header_e})
        return categories

    def _parse_footer_string(self, footer_text: str) -> List[Dict[str, str]]:
        if not footer_text: return []
        items = []
        for part in footer_text.split(','):
            if '-' in part: value, text = part.split('-', 1); items.append({'value': value.strip(), 'text': text.strip()})
        return items

    def _extract_fields(self, fields_container_node: Optional[etree._Element], all_categories: List[dict]) -> List[dict]:
        if fields_container_node is None: return []
        fields, raw_field_map = [], {}
        for match in re.finditer(r'<field(?:\s+[^>]*)?>.*?</field>', self.raw_content, re.DOTALL):
            name_match = re.search(r'name\s*=\s*["\']([^"\']+)["\']', match.group(0))
            if name_match and name_match.group(1) not in raw_field_map: raw_field_map[name_match.group(1)] = match.group(0)
        ns, xpaths = (self.ns_map, ('./df:field', 'df:header', 'df:label', 'df:footer', 'df:items', 'df:item', 'df:text')) if self.ns_map and 'df' in self.ns_map else (None, ('./field', 'header', 'label', 'footer', 'items', 'item', 'text'))
        field_xpath, header_xpath, label_xpath, footer_xpath, items_xpath, item_xpath, text_xpath = xpaths
        for f_node in fields_container_node.xpath(field_xpath, namespaces=ns):
            header_node, label_node, items_node, footer_node = f_node.find(header_xpath, namespaces=ns), f_node.find(label_xpath, namespaces=ns), f_node.find(items_xpath, namespaces=ns), f_node.find(footer_xpath, namespaces=ns)
            parsed_attributes = {k: self._resolve_string_with_source(v) for k, v in f_node.attrib.items()}
            field_name = parsed_attributes.get('name', {}).get('value', '')
            category_index = f_node.get('categoryIndex')
            matched_category_v, matched_category_e = '', ''
            if category_index and all_categories:
                for cat in all_categories:
                    if cat.get('index') == category_index: matched_category_v, matched_category_e = cat.get('header_v', ''), cat.get('header_e', ''); break
            items_options = []
            if items_node is not None:
                for item_child in items_node.findall(item_xpath, namespaces=ns):
                    text_node = item_child.find(text_xpath, namespaces=ns)
                    text_v, text_e = (text_node.get('v', ''), text_node.get('e', '')) if text_node is not None else ('', '')
                    items_options.append({'value': item_child.get('value'), 'text_v': text_v, 'text_e': text_e})
            if not items_options and footer_node is not None:
                parsed_v, parsed_e = self._parse_footer_string(footer_node.get('v', '')), self._parse_footer_string(footer_node.get('e', ''))
                for item_v in parsed_v:
                    matching_e_item = next((item for item in parsed_e if item['value'] == item_v['value']), None)
                    items_options.append({'value': item_v['value'], 'text_v': item_v['text'], 'text_e': matching_e_item['text'] if matching_e_item else ''})
            fields.append({'name': field_name, 'header_v': self._resolve_string_with_source(header_node.get('v') if header_node is not None else None).get('value', ''), 'header_e': self._resolve_string_with_source(header_node.get('e') if header_node is not None else None).get('value', ''), 'label': self._resolve_string_with_source(label_node.get('v') if label_node is not None else None).get('value', ''), 'controller': self._resolve_string_with_source(items_node.get('controller') if items_node is not None else None).get('value', ''), 'raw_xml': raw_field_map.get(field_name, etree.tostring(f_node, pretty_print=True, encoding='unicode')), 'categoryIndex': category_index, 'category_v': matched_category_v, 'category_e': matched_category_e, 'parsed_data': {'attributes': parsed_attributes, 'header_v': self._resolve_string_with_source(header_node.get('v') if header_node is not None else None), 'header_e': self._resolve_string_with_source(header_node.get('e') if header_node is not None else None), 'label': self._resolve_string_with_source(label_node.get('v') if label_node is not None else None), 'controller': self._resolve_string_with_source(items_node.get('controller') if items_node is not None else None), 'items_options': items_options}})
        return fields
        
    def _extract_forms(self, all_categories: List[dict]) -> List[dict]:
        if self.root is None: return []
        forms, ns = [], self.ns_map if self.ns_map and 'df' in self.ns_map else None
        forms_xpath, header_xpath, fields_xpath = ('.//df:forms/df:form', 'df:header', 'df:fields') if ns else ('.//forms/form', 'header', 'fields')
        for f_node in self.root.xpath(forms_xpath, namespaces=ns):
            header_node = f_node.find(header_xpath, namespaces=ns)
            forms.append({'id': f_node.get('id', ''), 'reportFile': f_node.get('reportFile', ''), 'templateFile': f_node.get('templateFile', ''), 'commandArgument': f_node.get('commandArgument', ''), 'header_v': header_node.get('v', '') if header_node is not None else '', 'header_e': header_node.get('e', '') if header_node is not None else '', 'fields': self._extract_fields(f_node.find(fields_xpath, namespaces=ns), all_categories), 'raw_xml': etree.tostring(f_node, pretty_print=True, encoding='unicode')})
        return forms

    def _return_failure(self, error_message: str, content: str) -> Dict[str, Any]:
        return {"dir_info": {"error": error_message}, "fields": [], "forms": [], "final_doctype": self.final_doctype_full, "full_content": "", "original_content": content, "entity_map": self.entity_map, "entities": [], "entity_references": [], "categories": [], "analysis_failed": True}

# ================================
# API Wrapper
# ================================
class FileParser:
    @staticmethod
    def extract_entity_info(file_path: str) -> Dict[str, Any]:
        if file_path.lower().endswith('.ent'): return FileParser._parse_ent_file(file_path)
        parser = _Parser(file_path)
        return parser.parse()

    @staticmethod
    def _parse_ent_file(file_path: str) -> Dict[str, Any]:
        try:
            content = _safe_read_file(file_path)
            base_dir = os.path.dirname(file_path)
            resolver = _Parser(file_path)
            resolver.raw_content = f'<!DOCTYPE root [ {content} ]>'
            resolver._process_doctype_and_entities()
            dir_info = {"name": os.path.splitext(os.path.basename(file_path))[0], "type": "Entity", "entity_count": len([e for e in resolver.entity_structures if not e['is_parameter']]), "parameter_entity_count": len([e for e in resolver.entity_structures if e['is_parameter']])}
            return {"dir_info": dir_info, "fields": [], "forms": [], "categories": [], "final_doctype": None, "full_content": content, "original_content": content, "entity_map": resolver.entity_map, "entities": resolver.entity_structures, "entity_references": sorted(list(set(RE_ENTITY_REF.findall(content)))), "analysis_failed": False}
        except Exception as e:
            logger.error(f"Lỗi khi phân tích file .ent: {file_path}", exc_info=True)
            return {"dir_info": {"error": f"Lỗi phân tích file .ent: {e}"}, "fields": [], "forms": [], "final_doctype": None, "full_content": "", "original_content": "", "entity_map": dict(XML_BUILTIN_ENTITIES), "entities": [], "entity_references": [], "categories": [], "analysis_failed": True}
